{"name": "austimo-desktop", "version": "1.0.0", "description": "Sistema Híbrido de Evaluación de Autismo - Aplicación de Escritorio", "main": "main.js", "homepage": "./", "scripts": {"start": "electron .", "dev": "electron . --dev", "build": "electron-builder", "build-win": "electron-builder --win", "build-mac": "electron-builder --mac", "build-linux": "electron-builder --linux", "dist": "npm run build", "pack": "electron-builder --dir", "postinstall": "electron-builder install-app-deps"}, "build": {"appId": "com.austimo.desktop", "productName": "Austimo - Evaluación de Autismo", "directories": {"output": "dist"}, "files": ["main.js", "preload.js", "renderer.js", "app/**/*", "python-backend/**/*", "node_modules/**/*"], "extraResources": [{"from": "../austimo", "to": "python-backend", "filter": ["**/*", "!**/__pycache__/**/*", "!**/*.pyc"]}], "win": {"target": "nsis", "icon": "assets/icon.ico"}, "mac": {"target": "dmg", "icon": "assets/icon.icns"}, "linux": {"target": "AppImage", "icon": "assets/icon.png"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true}}, "devDependencies": {"electron": "^27.0.0", "electron-builder": "^24.6.4"}, "dependencies": {"child_process": "^1.0.2", "electron-is-dev": "^2.0.0", "path-extra": "^4.3.0"}, "author": "Tu Nombre", "license": "MIT"}