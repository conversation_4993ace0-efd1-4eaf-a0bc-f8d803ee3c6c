<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Austimo - Sistema de Evaluación de Autismo</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <!-- Estilos específicos para Electron -->
    <style>
        .electron-app {
            user-select: none; /* Prevenir selección de texto accidental */
        }
        
        .electron-app .selectable {
            user-select: text; /* Permitir selección en elementos específicos */
        }
        
        /* Ajustes para la barra de título nativa */
        .navbar {
            -webkit-app-region: drag; /* Permitir arrastrar la ventana */
        }
        
        .nav-container button,
        .nav-container a {
            -webkit-app-region: no-drag; /* Prevenir arrastre en botones */
        }
        
        /* Indicador de aplicación de escritorio */
        .desktop-indicator {
            position: fixed;
            bottom: 10px;
            right: 10px;
            background: rgba(37, 99, 235, 0.1);
            color: #2563eb;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.75rem;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <!-- Indicador de aplicación de escritorio -->
    <div class="desktop-indicator" id="desktop-indicator" style="display: none;">
        <i class="fas fa-desktop"></i> Aplicación de Escritorio
    </div>

    <!-- Navegación -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <i class="fas fa-brain"></i>
                <span class="nav-title">Sistema Híbrido de Evaluación de Autismo</span>
            </div>
            <div class="nav-menu" id="nav-menu">
                <button class="nav-btn" id="nav-home">
                    <i class="fas fa-home"></i>
                    <span>Inicio</span>
                </button>
                <button class="nav-btn" id="nav-autism">
                    <i class="fas fa-book-open"></i>
                    <span>Sobre el Autismo</span>
                </button>
                <button class="nav-btn" id="nav-info">
                    <i class="fas fa-info-circle"></i>
                    <span>Información Técnica</span>
                </button>
                <button class="nav-btn" id="nav-graficos" onclick="window.location.href='PaginaGraficos/index.html'">
                    <i class="fas fa-chart-bar"></i>
                    <span>Gráficos</span>
                </button>
            </div>
            <button class="nav-toggle" id="nav-toggle">
                <i class="fas fa-bars"></i>
            </button>
        </div>
    </nav>

    <!-- Indicador de conexión -->
    <div class="connection-status" id="connection-status">
        <i class="fas fa-circle"></i>
        <span id="connection-text">Verificando conexión...</span>
    </div>

    <!-- Resto del contenido HTML original -->
    <!-- (Mantener todo el contenido de las vistas tal como está) -->
    
    <!-- Pantalla de Bienvenida -->
    <div id="welcome-view" class="view active">
        <div class="container">
            <!-- Header principal -->
            <div class="welcome-header">
                <div class="header-icon">
                    <i class="fas fa-brain"></i>
                </div>
                <h1 class="main-title">Sistema Híbrido de Evaluación de Autismo</h1>
                <p class="subtitle">DBSCAN + GMM + ML + Clustering Interactivo | Edades 1-16 años</p>
                
                <div class="badges">
                    <div class="badge">
                        <i class="fas fa-check-circle"></i>
                        Científicamente Validado
                    </div>
                    <div class="badge">
                        <i class="fas fa-shield-alt"></i>
                        Enfoque Híbrido Completo
                    </div>
                    <div class="badge">
                        <i class="fas fa-chart-scatter"></i>
                        Clustering Interactivo
                    </div>
                    <div class="badge">
                        <i class="fas fa-desktop"></i>
                        Aplicación de Escritorio
                    </div>
                    <div class="badge">
                        <i class="fas fa-clock"></i>
                        5-10 minutos
                    </div>
                </div>
            </div>

            <!-- Resto del contenido de bienvenida -->
            <!-- (Copiar todo el contenido original) -->
            
            <!-- Características principales -->
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon blue">
                        <i class="fas fa-bullseye"></i>
                    </div>
                    <h3>Detección Precisa</h3>
                    <p>Combina DBSCAN para casos atípicos, GMM para clasificación de riesgos, y modelos de ML para máxima precisión, adaptado específicamente para el rango de 1-16 años.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon green">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <h3>Análisis Adaptativo</h3>
                    <p>Considera las diferencias de desarrollo entre bebés, niños preescolares, escolares y adolescentes. Incluye árboles de decisión y análisis de importancia de características.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon purple">
                        <i class="fas fa-chart-scatter"></i>
                    </div>
                    <h3>Clustering Interactivo</h3>
                    <p>Visualiza la posición de su hijo/a en relación con patrones históricos de datos. Muestra clusters de riesgo con interpretación clara y recomendaciones específicas.</p>
                </div>
            </div>

            <!-- Información del cuestionario -->
            <div class="info-card">
                <div class="info-header">
                    <i class="fas fa-info-circle"></i>
                    <h3>Sobre el Cuestionario Q-CHAT-10 + Clustering</h3>
                </div>
                <div class="info-content">
                    <div class="info-section">
                        <h4>Características:</h4>
                        <ul class="check-list">
                            <li><i class="fas fa-check"></i> 10 preguntas basadas en evidencia científica</li>
                            <li><i class="fas fa-check"></i> Enfoque en comunicación social y atención conjunta</li>
                            <li><i class="fas fa-check"></i> Adaptado para rango extendido 1-16 años</li>
                            <li><i class="fas fa-check"></i> Detección de casos atípicos especializados</li>
                            <li><i class="fas fa-check"></i> Análisis con múltiples algoritmos de ML</li>
                            <li><i class="fas fa-check"></i> Visualización interactiva de clusters</li>
                            <li><i class="fas fa-check"></i> <strong>Aplicación de escritorio independiente</strong></li>
                        </ul>
                    </div>
                    <div class="info-section">
                        <h4>Áreas Evaluadas:</h4>
                        <div class="tags">
                            <span class="tag">Comunicación Social</span>
                            <span class="tag">Atención Conjunta</span>
                            <span class="tag">Juego Simbólico</span>
                            <span class="tag">Comunicación No Verbal</span>
                            <span class="tag">Empatía</span>
                            <span class="tag">Lenguaje</span>
                            <span class="tag">Clustering de Riesgo</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Botones de acción -->
            <div class="action-buttons">
                <button class="btn btn-primary btn-large" id="start-assessment-btn">
                    <i class="fas fa-arrow-right"></i>
                    Comenzar Evaluación
                </button>
                <button class="btn btn-outline btn-large" id="autism-info-btn">
                    <i class="fas fa-book-open"></i>
                    Sobre el Autismo
                </button>
                <button class="btn btn-outline btn-large" id="tech-info-btn">
                    <i class="fas fa-lightbulb"></i>
                    Información Técnica
                </button>
            </div>

            <!-- Disclaimer -->
            <div class="disclaimer">
                <i class="fas fa-exclamation-triangle"></i>
                <div>
                    <strong>Importante:</strong> Esta herramienta es para fines informativos y de detección temprana. No reemplaza el diagnóstico profesional. Siempre consulte con un especialista en desarrollo infantil para una evaluación completa.
                </div>
            </div>
        </div>
    </div>

    <!-- Resto de las vistas (copiar todo el contenido original) -->
    <!-- ... -->

    <!-- Scripts -->
    <script src="renderer.js"></script>
    <script src="script.js"></script>
    <script src="clustering_ui.js"></script>
    
    <script>
        // Script específico para Electron
        document.addEventListener('DOMContentLoaded', () => {
            // Mostrar indicador de aplicación de escritorio
            if (window.isElectron) {
                const indicator = document.getElementById('desktop-indicator')
                if (indicator) {
                    indicator.style.display = 'block'
                }
            }
            
            // Personalizar comportamiento para aplicación de escritorio
            console.log('🖥️ Aplicación de escritorio Austimo iniciada')
        })
    </script>
</body>
</html>
