@echo off
echo 🔄 Copiando archivos de la aplicación web...

REM Crear directorios si no existen
if not exist "app" mkdir app
if not exist "assets" mkdir assets

REM Copiar archivos principales de la web
copy "..\austimo\index.html" "app\"
copy "..\austimo\styles.css" "app\"
copy "..\austimo\script.js" "app\"
copy "..\austimo\clustering_ui.js" "app\"

REM Copiar carpeta de gráficos
if not exist "app\PaginaGraficos" mkdir app\PaginaGraficos
xcopy "..\austimo\PaginaGraficos" "app\PaginaGraficos" /E /I /Y

echo ✅ Archivos copiados correctamente
pause
