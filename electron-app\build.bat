@echo off
echo 🏗️ Construyendo aplicación Austimo para distribución...

REM Verificar Node.js
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js no está instalado
    pause
    exit /b 1
)

REM Instalar dependencias si es necesario
if not exist "node_modules" (
    echo 📦 Instalando dependencias...
    npm install
)

REM Copiar archivos actualizados
echo 📁 Copiando archivos actualizados...
call copy-files.bat

REM Construir aplicación
echo 🔨 Construyendo aplicación...
npm run build-win

echo ✅ Construcción completada
echo 📁 Los archivos están en la carpeta 'dist'
pause
