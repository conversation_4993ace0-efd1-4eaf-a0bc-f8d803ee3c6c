@echo off
echo 🚀 Iniciando Austimo en modo desarrollo...

REM Verificar que Node.js esté instalado
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js no está instalado
    echo Por favor, instale Node.js desde https://nodejs.org/
    pause
    exit /b 1
)

REM Verificar que las dependencias estén instaladas
if not exist "node_modules" (
    echo 📦 Instalando dependencias...
    npm install
)

REM Copiar archivos actualizados
echo 📁 Copiando archivos actualizados...
call copy-files.bat

REM Iniciar aplicación
echo 🖥️ Iniciando aplicación Electron...
npm run dev

pause
