{"name": "electron-is-dev", "version": "3.0.1", "description": "Check if Electron is running in development", "license": "MIT", "repository": "sindresorhus/electron-is-dev", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": {"types": "./index.d.ts", "default": "./index.js"}, "sideEffects": false, "engines": {"node": ">=18"}, "scripts": {"test": "xo && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["electron", "dev", "development", "mode", "prod", "production", "detect", "check", "debug", "app"], "devDependencies": {"tsd": "^0.30.3", "xo": "^0.56.0"}}