{"name": "path-extra", "version": "4.3.0", "description": "path-extra contains methods that aren't included in the vanilla Node.js path package.", "homepage": "https://github.com/jprichardson/node-path-extra", "repository": {"type": "git", "url": "https://github.com/jprichardson/node-path-extra"}, "keywords": ["fs", "file", "file system", "path"], "author": "<PERSON> <jp<PERSON><PERSON><EMAIL>>", "license": "MIT", "devDependencies": {"mocha": "*", "standard": "*"}, "main": "./lib/path.js", "typings": "./types/index.d.ts", "scripts": {"test": "standard && mocha"}, "dependencies": {"replace-ext": "^1.0.0", "escape-string-regexp": "^1.0.5"}}