const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require("electron")

// Exponer APIs seguras al renderer process
contextBridge.exposeInMainWorld("electronAPI", {
  // Funciones de la aplicación
  newAssessment: () => ipcRenderer.invoke("new-assessment"),
  showTechInfo: () => ipcRenderer.invoke("show-tech-info"),

  // Eventos del menú
  onNewAssessment: (callback) => ipcRenderer.on("new-assessment", callback),
  onShowTechInfo: (callback) => ipcRenderer.on("show-tech-info", callback),

  // Información del sistema
  platform: process.platform,
  versions: process.versions,

  // Funciones de utilidad
  openExternal: (url) => ipcRenderer.invoke("open-external", url),
  showMessageBox: (options) => ipcRenderer.invoke("show-message-box", options),
})

// Log de inicialización
console.log("🔧 Preload script cargado")
console.log("🖥️ Plataforma:", process.platform)
console.log("⚡ Electron:", process.versions.electron)
console.log("🌐 Node:", process.versions.node)
