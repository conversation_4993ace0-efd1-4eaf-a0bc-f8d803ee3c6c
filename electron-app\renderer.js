// Script adicional para funcionalidades específicas de Electron

// Verificar si estamos en Electron
const isElectron = typeof window !== "undefined" && window.electronAPI

// Declarar las variables resetAssessment, showView y showNotification
let resetAssessment
let showView
let showNotification

if (isElectron) {
  console.log("🖥️ Ejecutándose en Electron")

  // Manejar eventos del menú
  window.electronAPI.onNewAssessment(() => {
    console.log("📝 Nueva evaluación solicitada desde el menú")
    if (typeof resetAssessment === "function") {
      resetAssessment()
    }
  })

  window.electronAPI.onShowTechInfo(() => {
    console.log("ℹ️ Información técnica solicitada desde el menú")
    if (typeof showView === "function") {
      showView("info")
    }
  })

  // Personalizar comportamiento para Electron
  document.addEventListener("DOMContentLoaded", () => {
    // Agregar clase CSS para estilos específicos de Electron
    document.body.classList.add("electron-app")

    // Mostrar información de la plataforma en desarrollo
    if (window.electronAPI.platform) {
      console.log("🖥️ Plataforma detectada:", window.electronAPI.platform)
    }

    // Personalizar título de la ventana
    document.title = "Austimo - Sistema de Evaluación de Autismo"
  })

  // Manejar enlaces externos
  document.addEventListener("click", (event) => {
    const target = event.target.closest("a")
    if (target && target.href && target.href.startsWith("http")) {
      event.preventDefault()
      window.electronAPI.openExternal(target.href)
    }
  })
} else {
  console.log("🌐 Ejecutándose en navegador web")
}

// Función para mostrar notificaciones específicas de Electron
function showElectronNotification(message, type = "info") {
  if (isElectron) {
    // Usar el sistema de notificaciones nativo si está disponible
    if ("Notification" in window) {
      new Notification("Austimo", {
        body: message,
        icon: "./assets/icon.png",
      })
    }
  }

  // Fallback a notificaciones web normales
  if (typeof showNotification === "function") {
    showNotification(message, type)
  }
}

// Exportar funciones para uso global
if (typeof window !== "undefined") {
  window.showElectronNotification = showElectronNotification
  window.isElectron = isElectron
}
