const { app, BrowserWindow, Menu, dialog, shell } = require("electron")
const path = require("path")
const isDev = require("electron-is-dev")
const { spawn } = require("child_process")

// Variables globales
let mainWindow
let pythonProcess = null
const pythonPort = 5000

// Función para crear la ventana principal
function createWindow() {
  // Crear la ventana del navegador
  mainWindow = new BrowserWindow({
    width: 1400,
    height: 900,
    minWidth: 1200,
    minHeight: 800,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, "preload.js"),
      webSecurity: false, // Necesario para CORS con el backend local
    },
    icon: path.join(__dirname, "assets", "icon.png"),
    show: false, // No mostrar hasta que esté listo
    titleBarStyle: "default",
    autoHideMenuBar: false,
  })

  // Configurar menú de la aplicación
  createMenu()

  // Cargar la aplicación
  const startUrl = isDev ? "http://localhost:3000" : `file://${path.join(__dirname, "app/index.html")}`

  // En producción, cargar desde los archivos locales
  if (!isDev) {
    mainWindow.loadFile(path.join(__dirname, "app/index.html"))
  } else {
    mainWindow.loadURL(startUrl)
  }

  // Mostrar ventana cuando esté lista
  mainWindow.once("ready-to-show", () => {
    mainWindow.show()

    // Abrir DevTools en desarrollo
    if (isDev) {
      mainWindow.webContents.openDevTools()
    }
  })

  // Manejar cierre de ventana
  mainWindow.on("closed", () => {
    mainWindow = null
  })

  // Manejar enlaces externos
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    shell.openExternal(url)
    return { action: "deny" }
  })

  // Prevenir navegación no deseada
  mainWindow.webContents.on("will-navigate", (event, navigationUrl) => {
    const parsedUrl = new URL(navigationUrl)

    if (parsedUrl.origin !== "http://localhost:5000" && parsedUrl.origin !== "file://") {
      event.preventDefault()
    }
  })
}

// Función para crear el menú de la aplicación
function createMenu() {
  const template = [
    {
      label: "Archivo",
      submenu: [
        {
          label: "Nueva Evaluación",
          accelerator: "CmdOrCtrl+N",
          click: () => {
            if (mainWindow) {
              mainWindow.webContents.send("new-assessment")
            }
          },
        },
        {
          label: "Imprimir Resultados",
          accelerator: "CmdOrCtrl+P",
          click: () => {
            if (mainWindow) {
              mainWindow.webContents.print()
            }
          },
        },
        { type: "separator" },
        {
          label: "Salir",
          accelerator: process.platform === "darwin" ? "Cmd+Q" : "Ctrl+Q",
          click: () => {
            app.quit()
          },
        },
      ],
    },
    {
      label: "Ver",
      submenu: [
        { role: "reload", label: "Recargar" },
        { role: "forceReload", label: "Forzar Recarga" },
        { role: "toggleDevTools", label: "Herramientas de Desarrollador" },
        { type: "separator" },
        { role: "resetZoom", label: "Zoom Normal" },
        { role: "zoomIn", label: "Acercar" },
        { role: "zoomOut", label: "Alejar" },
        { type: "separator" },
        { role: "togglefullscreen", label: "Pantalla Completa" },
      ],
    },
    {
      label: "Ayuda",
      submenu: [
        {
          label: "Sobre Austimo",
          click: () => {
            dialog.showMessageBox(mainWindow, {
              type: "info",
              title: "Sobre Austimo",
              message: "Sistema Híbrido de Evaluación de Autismo",
              detail:
                "Versión 1.0.0\n\nSistema completo con DBSCAN + GMM + ML + Clustering\nPara edades de 1-16 años\n\n© 2024 - Todos los derechos reservados",
            })
          },
        },
        {
          label: "Información Técnica",
          click: () => {
            if (mainWindow) {
              mainWindow.webContents.send("show-tech-info")
            }
          },
        },
      ],
    },
  ]

  // Ajustes específicos para macOS
  if (process.platform === "darwin") {
    template.unshift({
      label: app.getName(),
      submenu: [
        { role: "about", label: "Acerca de Austimo" },
        { type: "separator" },
        { role: "services", label: "Servicios" },
        { type: "separator" },
        { role: "hide", label: "Ocultar Austimo" },
        { role: "hideothers", label: "Ocultar Otros" },
        { role: "unhide", label: "Mostrar Todo" },
        { type: "separator" },
        { role: "quit", label: "Salir de Austimo" },
      ],
    })
  }

  const menu = Menu.buildFromTemplate(template)
  Menu.setApplicationMenu(menu)
}

// Función para iniciar el backend de Python
function startPythonBackend() {
  return new Promise((resolve, reject) => {
    console.log("🐍 Iniciando backend de Python...")

    // Determinar la ruta del script Python
    const pythonScriptPath = isDev
      ? path.join(__dirname, "..", "austimo", "app.py")
      : path.join(process.resourcesPath, "python-backend", "app.py")

    console.log("📁 Ruta del script Python:", pythonScriptPath)

    // Determinar el comando Python
    const pythonCommand = process.platform === "win32" ? "python" : "python3"

    // Iniciar el proceso Python
    pythonProcess = spawn(pythonCommand, [pythonScriptPath], {
      cwd: path.dirname(pythonScriptPath),
      stdio: ["pipe", "pipe", "pipe"],
    })

    // Manejar salida del proceso
    pythonProcess.stdout.on("data", (data) => {
      console.log("🐍 Python stdout:", data.toString())

      // Detectar cuando el servidor esté listo
      if (data.toString().includes("Running on")) {
        console.log("✅ Backend de Python iniciado correctamente")
        resolve()
      }
    })

    pythonProcess.stderr.on("data", (data) => {
      console.error("🐍 Python stderr:", data.toString())
    })

    pythonProcess.on("close", (code) => {
      console.log(`🐍 Proceso Python terminado con código: ${code}`)
      pythonProcess = null
    })

    pythonProcess.on("error", (error) => {
      console.error("❌ Error al iniciar Python:", error)
      reject(error)
    })

    // Timeout de 10 segundos
    setTimeout(() => {
      if (pythonProcess && !pythonProcess.killed) {
        console.log("✅ Asumiendo que Python está listo (timeout)")
        resolve()
      }
    }, 10000)
  })
}

// Función para detener el backend de Python
function stopPythonBackend() {
  if (pythonProcess && !pythonProcess.killed) {
    console.log("🛑 Deteniendo backend de Python...")
    pythonProcess.kill("SIGTERM")
    pythonProcess = null
  }
}

// Función para verificar si Python está disponible
function checkPythonAvailability() {
  return new Promise((resolve) => {
    const pythonCommand = process.platform === "win32" ? "python" : "python3"
    const testProcess = spawn(pythonCommand, ["--version"], { stdio: "pipe" })

    testProcess.on("close", (code) => {
      resolve(code === 0)
    })

    testProcess.on("error", () => {
      resolve(false)
    })
  })
}

// Evento: Aplicación lista
app.whenReady().then(async () => {
  console.log("🚀 Aplicación Electron iniciada")

  // Verificar disponibilidad de Python
  const pythonAvailable = await checkPythonAvailability()

  if (!pythonAvailable) {
    dialog.showErrorBox(
      "Python no encontrado",
      "Python no está instalado o no está disponible en el PATH del sistema.\n\nPor favor, instale Python 3.7+ y reinicie la aplicación.",
    )
    app.quit()
    return
  }

  try {
    // Iniciar backend de Python
    await startPythonBackend()

    // Crear ventana principal
    createWindow()

    console.log("✅ Aplicación completamente iniciada")
  } catch (error) {
    console.error("❌ Error al iniciar la aplicación:", error)
    dialog.showErrorBox(
      "Error de Inicio",
      "No se pudo iniciar el backend de Python.\n\nVerifique que todas las dependencias estén instaladas.",
    )
    app.quit()
  }
})

// Evento: Todas las ventanas cerradas
app.on("window-all-closed", () => {
  stopPythonBackend()

  if (process.platform !== "darwin") {
    app.quit()
  }
})

// Evento: Aplicación activada (macOS)
app.on("activate", () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow()
  }
})

// Evento: Antes de salir
app.on("before-quit", () => {
  stopPythonBackend()
})

// Manejar errores no capturados
process.on("uncaughtException", (error) => {
  console.error("❌ Error no capturado:", error)
})

process.on("unhandledRejection", (reason, promise) => {
  console.error("❌ Promesa rechazada no manejada:", reason)
})
